"use client";

import * as faceapi from "face-api.js";

export class FaceDetectionService {
  private static instance: FaceDetectionService;
  private isInitialized = false;
  private modelsLoaded = false;
  private initializationError: string | null = null;

  private constructor() {}

  static getInstance(): FaceDetectionService {
    if (!FaceDetectionService.instance) {
      FaceDetectionService.instance = new FaceDetectionService();
    }
    return FaceDetectionService.instance;
  }

  private async checkModelAvailability(): Promise<boolean> {
    try {
      const response = await fetch(
        "/models/tiny_face_detector_model-weights_manifest.json"
      );
      return response.ok;
    } catch {
      return false;
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      const modelsAvailable = await this.checkModelAvailability();

      if (!modelsAvailable) {
        this.initializationError =
          "FaceAPI models not found. Please download the required model files.";
        console.warn(
          "FaceAPI models not available. Face detection will be disabled."
        );
        this.isInitialized = true; // Mark as initialized but without models
        return;
      }

      // Load face detection models
      const MODEL_URL = "/models";

      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
        faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),
        faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL),
      ]);

      this.modelsLoaded = true;
      this.isInitialized = true;
      console.log("FaceAPI models loaded successfully");
    } catch (error) {
      console.error("Failed to load FaceAPI models:", error);
      this.initializationError =
        "Failed to load FaceAPI models. Please check model files.";
      this.isInitialized = true; // Mark as initialized but with error
    }
  }

  async detectFaces(
    input: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
  ): Promise<
    faceapi.WithFaceLandmarks<
      { detection: faceapi.FaceDetection },
      faceapi.FaceLandmarks68
    >[]
  > {
    if (!this.isInitialized || !this.modelsLoaded) {
      return []; // Return empty array instead of throwing error
    }

    try {
      const detections = await faceapi
        .detectAllFaces(input, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks();

      return detections;
    } catch (error) {
      console.error("Face detection error:", error);
      return [];
    }
  }

  async detectFacesWithExpressions(
    input: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
  ): Promise<
    faceapi.WithFaceExpressions<
      faceapi.WithFaceLandmarks<
        { detection: faceapi.FaceDetection },
        faceapi.FaceLandmarks68
      >
    >[]
  > {
    if (!this.isInitialized || !this.modelsLoaded) {
      return []; // Return empty array instead of throwing error
    }

    try {
      const detections = await faceapi
        .detectAllFaces(input, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceExpressions();

      console.log("detections", detections);
      return detections;
    } catch (error) {
      console.error("Face detection with expressions error:", error);
      return [];
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.modelsLoaded;
  }

  getInitializationError(): string | null {
    return this.initializationError;
  }

  hasModels(): boolean {
    return this.modelsLoaded;
  }
}

// Export singleton instance
export const faceDetectionService = FaceDetectionService.getInstance();

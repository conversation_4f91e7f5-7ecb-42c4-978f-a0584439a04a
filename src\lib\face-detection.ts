"use client";

import * as faceapi from "face-api.js";

export class FaceDetectionService {
  private static instance: FaceDetectionService;
  private isInitialized = false;
  private modelsLoaded = false;
  private initializationError: string | null = null;

  private constructor() {}

  static getInstance(): FaceDetectionService {
    if (!FaceDetectionService.instance) {
      FaceDetectionService.instance = new FaceDetectionService();
    }
    return FaceDetectionService.instance;
  }

  private async checkModelAvailability(): Promise<boolean> {
    try {
      const response = await fetch(
        "/models/tiny_face_detector_model-weights_manifest.json"
      );
      return response.ok;
    } catch {
      return false;
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      const modelsAvailable = await this.checkModelAvailability();

      if (!modelsAvailable) {
        this.initializationError =
          "FaceAPI models not found. Please download the required model files.";
        console.warn(
          "FaceAPI models not available. Face detection will be disabled."
        );
        this.isInitialized = true; // Mark as initialized but without models
        return;
      }

      // Load only face detection model (no landmarks or expressions)
      const MODEL_URL = "/models";

      await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);

      this.modelsLoaded = true;
      this.isInitialized = true;
      console.log("FaceAPI models loaded successfully");
    } catch (error) {
      console.error("Failed to load FaceAPI models:", error);
      this.initializationError =
        "Failed to load FaceAPI models. Please check model files.";
      this.isInitialized = true; // Mark as initialized but with error
    }
  }

  async detectSingleFace(
    input: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
  ): Promise<faceapi.FaceDetection | null> {
    if (!this.isInitialized || !this.modelsLoaded) {
      return null; // Return null instead of throwing error
    }

    try {
      const detection = await faceapi.detectSingleFace(
        input,
        new faceapi.TinyFaceDetectorOptions()
      );

      console.log("single face detection", detection);
      return detection || null;
    } catch (error) {
      console.error("Single face detection error:", error);
      return null;
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.modelsLoaded;
  }

  getInitializationError(): string | null {
    return this.initializationError;
  }

  hasModels(): boolean {
    return this.modelsLoaded;
  }
}

// Export singleton instance
export const faceDetectionService = FaceDetectionService.getInstance();

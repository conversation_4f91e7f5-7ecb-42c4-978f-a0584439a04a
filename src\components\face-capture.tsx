"use client";

import { useFaceDetection } from "@/hooks/use-face-detection";
import React, { useState } from "react";

type Props = {
  videoRef: React.RefObject<HTMLVideoElement | null>;
  isActive: boolean;
};

interface CapturedImage {
  id: string;
  dataURL: string;
  timestamp: Date;
}

const FaceCapture = ({ videoRef, isActive }: Props) => {
  const { captureFaceImage, isReady } = useFaceDetection();
  const [capturedImages, setCapturedImages] = useState<CapturedImage[]>([]);
  const [isCapturing, setIsCapturing] = useState(false);
  const [marginPercent, setMarginPercent] = useState(30);
  const [previewImage, setPreviewImage] = useState<CapturedImage | null>(null);

  const handleCapture = async () => {
    if (!videoRef.current || !isActive || !isReady || isCapturing) {
      return;
    }

    setIsCapturing(true);
    try {
      const dataURL = await captureFaceImage(videoRef.current, marginPercent);

      if (dataURL) {
        const newImage: CapturedImage = {
          id: Date.now().toString(),
          dataURL,
          timestamp: new Date(),
        };

        setCapturedImages((prev) => [newImage, ...prev]);
        console.log("Face captured successfully");
      } else {
        console.log("No face detected for capture");
      }
    } catch (error) {
      console.error("Capture failed:", error);
    } finally {
      setIsCapturing(false);
    }
  };

  const handleDownload = (image: CapturedImage) => {
    const link = document.createElement("a");
    link.href = image.dataURL;
    link.download = `face-capture-${image.id}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = (imageId: string) => {
    setCapturedImages((prev) => prev.filter((img) => img.id !== imageId));
  };

  const clearAll = () => {
    setCapturedImages([]);
  };

  return (
    <div className="w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow-lg">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-3">Face Capture</h3>

        {/* Margin Control */}
        <div className="mb-3">
          <label className="block text-sm font-medium mb-1">
            Margin: {marginPercent}%
          </label>
          <input
            type="range"
            min="10"
            max="50"
            value={marginPercent}
            onChange={(e) => setMarginPercent(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>10%</span>
            <span>50%</span>
          </div>
        </div>

        {/* Capture Button */}
        <button
          onClick={handleCapture}
          disabled={!isActive || !isReady || isCapturing}
          className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
            !isActive || !isReady || isCapturing
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          {isCapturing ? "Capturing..." : "Capture Face"}
        </button>

        {/* Status */}
        {!isActive && (
          <p className="text-sm text-gray-500 mt-2">Camera not active</p>
        )}
        {!isReady && isActive && (
          <p className="text-sm text-gray-500 mt-2">Face detection not ready</p>
        )}
      </div>

      {/* Captured Images */}
      {capturedImages.length > 0 && (
        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">
              Captured Images ({capturedImages.length})
            </h4>
            <button
              onClick={clearAll}
              className="text-sm text-red-500 hover:text-red-700"
            >
              Clear All
            </button>
          </div>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {capturedImages.map((image) => (
              <div
                key={image.id}
                className="flex items-center space-x-3 p-2 border rounded-lg"
              >
                <img
                  src={image.dataURL}
                  alt="Captured face"
                  className="w-16 h-16 object-cover rounded-lg"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-600 truncate">
                    {image.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setPreviewImage(image)}
                    className="text-sm text-green-500 hover:text-green-700"
                  >
                    Preview
                  </button>
                  <button
                    onClick={() => handleDownload(image)}
                    className="text-sm text-blue-500 hover:text-blue-700"
                  >
                    Download
                  </button>
                  <button
                    onClick={() => handleDelete(image.id)}
                    className="text-sm text-red-500 hover:text-red-700"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 max-w-md max-h-[80vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Preview</h3>
              <button
                onClick={() => setPreviewImage(null)}
                className="text-gray-500 hover:text-gray-700 text-xl"
              >
                ×
              </button>
            </div>
            <img
              src={previewImage.dataURL}
              alt="Face preview"
              className="w-full h-auto rounded-lg mb-4"
            />
            <div className="flex justify-between items-center text-sm text-gray-600 mb-4">
              <span>Captured: {previewImage.timestamp.toLocaleString()}</span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleDownload(previewImage)}
                className="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Download
              </button>
              <button
                onClick={() => {
                  handleDelete(previewImage.id);
                  setPreviewImage(null);
                }}
                className="flex-1 py-2 px-4 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FaceCapture;

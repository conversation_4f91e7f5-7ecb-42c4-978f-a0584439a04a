"use client";

import { useEffect, useState, useCallback } from "react";
import * as faceapi from "face-api.js";
import { faceDetectionService } from "@/lib/face-detection";

export interface FaceDetectionResult {
  detection: faceapi.FaceDetection;
}

export interface UseFaceDetectionReturn {
  isLoading: boolean;
  isReady: boolean;
  error: string | null;
  hasModels: boolean; // Added to track if models are available
  detectFace: (
    element: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
  ) => Promise<FaceDetectionResult | null>;
  drawDetection: (
    canvas: HTMLCanvasElement,
    detection: FaceDetectionResult | null,
    displaySize: { width: number; height: number }
  ) => void;
}

export function useFaceDetection(): UseFaceDetectionReturn {
  const [isLoading, setIsLoading] = useState(true);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasModels, setHasModels] = useState(false); // Track model availability

  useEffect(() => {
    const initializeFaceAPI = async () => {
      console.log("Initializing FaceAPI...");
      try {
        setIsLoading(true);
        setError(null);

        await faceDetectionService.initialize();

        const initError = faceDetectionService.getInitializationError();
        if (initError) {
          setError(initError);
        }

        const modelsAvailable = faceDetectionService.hasModels();
        setHasModels(modelsAvailable);
        setIsReady(modelsAvailable);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to initialize face detection";
        setError(errorMessage);
        console.error("FaceAPI initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeFaceAPI();
  }, []);

  const detectFace = useCallback(
    async (
      element: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
    ): Promise<FaceDetectionResult | null> => {
      if (!isReady) {
        return null;
      }
      console.log("detectFace isReady", isReady);

      try {
        const detection = await faceDetectionService.detectSingleFace(element);
        console.log("detection service", detection);

        if (detection) {
          return { detection };
        }
        return null;
      } catch (err) {
        console.error("Face detection error:", err);
        return null;
      } finally {
        setIsLoading(false);
        console.log("finally");
      }
    },
    [isReady]
  );

  const drawDetection = useCallback(
    (
      canvas: HTMLCanvasElement,
      detection: FaceDetectionResult | null,
      displaySize: { width: number; height: number }
    ) => {
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // Clear previous drawings
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Set canvas size to match display
      canvas.width = displaySize.width;
      canvas.height = displaySize.height;

      // If no detection, just clear and return
      if (!detection) return;

      // Resize detection to match display size
      const resizedDetection = faceapi.resizeResults(detection, displaySize);
      const box = resizedDetection.detection.box;

      // Draw bounding box
      ctx.strokeStyle = "#00ff00";
      ctx.lineWidth = 2;
      ctx.strokeRect(box.x, box.y, box.width, box.height);

      // Draw confidence score
      ctx.fillStyle = "#00ff00";
      ctx.font = "16px Arial";
      ctx.fillText(
        `${Math.round(resizedDetection.detection.score * 100)}%`,
        box.x,
        box.y > 20 ? box.y - 5 : box.y + 20
      );
    },
    []
  );

  return {
    isLoading,
    isReady,
    error,
    hasModels, // Return model availability status
    detectFace,
    drawDetection,
  };
}

"use client";

import { useEffect, useState, useCallback } from "react";
import * as faceapi from "face-api.js";
import { faceDetectionService } from "@/lib/face-detection";

export interface FaceDetectionResult {
  detection: faceapi.FaceDetection;
  landmarks: faceapi.FaceLandmarks68;
  expressions?: faceapi.FaceExpressions;
}

export interface UseFaceDetectionReturn {
  isLoading: boolean;
  isReady: boolean;
  error: string | null;
  hasModels: boolean; // Added to track if models are available
  detectFaces: (
    element: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
  ) => Promise<FaceDetectionResult[]>;
  drawDetections: (
    canvas: HTMLCanvasElement,
    detections: FaceDetectionResult[],
    displaySize: { width: number; height: number }
  ) => void;
}

export function useFaceDetection(): UseFaceDetectionReturn {
  const [isLoading, setIsLoading] = useState(true);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasModels, setHasModels] = useState(false); // Track model availability

  useEffect(() => {
    const initializeFaceAPI = async () => {
      console.log("Initializing FaceAPI...");
      try {
        setIsLoading(true);
        setError(null);

        await faceDetectionService.initialize();

        const initError = faceDetectionService.getInitializationError();
        if (initError) {
          setError(initError);
        }

        const modelsAvailable = faceDetectionService.hasModels();
        setHasModels(modelsAvailable);
        setIsReady(modelsAvailable);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to initialize face detection";
        setError(errorMessage);
        console.error("FaceAPI initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeFaceAPI();
  }, []);

  const detectFaces = useCallback(
    async (
      element: HTMLVideoElement | HTMLCanvasElement | HTMLImageElement
    ): Promise<FaceDetectionResult[]> => {
      if (!isReady) {
        return [];
      }
      console.log("detectFaces isReady", isReady);

      try {
        const detections =
          await faceDetectionService.detectFacesWithExpressions(element);
        console.log("detections service", detections);
        return detections.map((detection) => ({
          detection: detection.detection,
          landmarks: detection.landmarks,
          expressions: detection.expressions,
        }));
      } catch (err) {
        console.error("Face detection error:", err);
        return [];
      } finally {
        setIsLoading(false);
        console.log("finally");
      }
    },
    [isReady]
  );

  const drawDetections = useCallback(
    (
      canvas: HTMLCanvasElement,
      detections: FaceDetectionResult[],
      displaySize: { width: number; height: number }
    ) => {
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // Clear previous drawings
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Set canvas size to match display
      canvas.width = displaySize.width;
      canvas.height = displaySize.height;

      // Resize detections to match display size
      const resizedDetections = faceapi.resizeResults(detections, displaySize);

      // Draw face detection boxes
      resizedDetections.forEach((detection) => {
        const box = detection.detection.box;

        // Draw bounding box
        ctx.strokeStyle = "#00ff00";
        ctx.lineWidth = 2;
        ctx.strokeRect(box.x, box.y, box.width, box.height);

        // Draw confidence score
        ctx.fillStyle = "#00ff00";
        ctx.font = "16px Arial";
        ctx.fillText(
          `${Math.round(detection.detection.score * 100)}%`,
          box.x,
          box.y > 20 ? box.y - 5 : box.y + 20
        );

        // Draw landmarks
        if (detection.landmarks) {
          ctx.fillStyle = "#ff0000";
          detection.landmarks.positions.forEach((point) => {
            ctx.beginPath();
            ctx.arc(point.x, point.y, 1, 0, 2 * Math.PI);
            ctx.fill();
          });
        }

        // Draw top expression
        if (detection.expressions) {
          const expressions = Object.entries(detection.expressions);
          const topExpression = expressions.reduce((prev, current) =>
            prev[1] > current[1] ? prev : current
          );

          if (topExpression[1] > 0.5) {
            ctx.fillStyle = "#0066ff";
            ctx.font = "14px Arial";
            ctx.fillText(
              `${topExpression[0]}: ${Math.round(topExpression[1] * 100)}%`,
              box.x,
              box.y + box.height + 20
            );
          }
        }
      });
    },
    []
  );

  return {
    isLoading,
    isReady,
    error,
    hasModels, // Return model availability status
    detectFaces,
    drawDetections,
  };
}

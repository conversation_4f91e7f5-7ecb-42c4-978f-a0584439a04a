import React from "react";

type Props = {
  videoRef: React.RefObject<HTMLVideoElement | null>;
  setIsCameraActive: React.Dispatch<React.SetStateAction<boolean>>;
};

const WebcamCapture = ({ videoRef, setIsCameraActive }: Props) => {
  const handleStart = async () => {
    if (!videoRef.current) return;
    setIsCameraActive(true);

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: "user",
        },
        audio: false,
      });
      //   if (stream.active) {
      //     startWebcam();
      //   }

      videoRef.current.srcObject = stream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current?.play();
      };
    } catch (err) {
      console.error("Webcam access error:", err);
    }
  };

  const handleStop = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach((track) => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsCameraActive(false);
  };

  return (
    <>
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        playsInline
        muted
      />
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
        <button
          onClick={handleStart}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
        >
          Start Camera
        </button>
        <button
          onClick={handleStop}
          className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
        >
          Stop Camera
        </button>
      </div>
    </>
  );
};

export default WebcamCapture;

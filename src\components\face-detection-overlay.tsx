"use client";
import { useFaceDetection } from "@/hooks/use-face-detection";
import React, { useEffect, useState } from "react";

type Props = {
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  videoRef: React.RefObject<HTMLVideoElement | null>;
  isActive: boolean;
};

const FaceDetectionOverlay = ({ canvasRef, videoRef, isActive }: Props) => {
  const { isReady, detectFaces, drawDetections } = useFaceDetection();
  const [isVideoReady, setIsVideoReady] = React.useState(false);

  // Handle video ready state
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleCanPlay = () => {
      console.log("Video can play");
      setIsVideoReady(true);
    };

    const handleLoadedMetadata = () => {
      console.log("Video metadata loaded");
      if (video.readyState >= 2) {
        // HAVE_CURRENT_DATA
        setIsVideoReady(true);
      }
    };

    video.addEventListener("canplay", handleCanPlay);
    video.addEventListener("loadedmetadata", handleLoadedMetadata);

    // Check if video is already ready
    if (video.readyState >= 2) {
      setIsVideoReady(true);
    }

    return () => {
      video.removeEventListener("canplay", handleCanPlay);
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
    };
  }, [videoRef]);

  // Run face detection when everything is ready
  useEffect(() => {
    if (
      !isReady ||
      !isActive ||
      !isVideoReady ||
      !videoRef.current ||
      !canvasRef.current
    ) {
      console.log("Not ready for detection:", {
        isReady,
        isActive,
        isVideoReady,
        hasVideo: !!videoRef.current,
        hasCanvas: !!canvasRef.current,
      });
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    let animationFrameId: number;

    const detect = async () => {
      try {
        const detections = await detectFaces(video);

        if (detections.length > 0) {
          // Update canvas size to match video
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          // Draw detections
          drawDetections(canvas, detections, {
            width: video.videoWidth,
            height: video.videoHeight,
          });
        } else {
          // Clear canvas if no detections
          const ctx = canvas.getContext("2d");
          ctx?.clearRect(0, 0, canvas.width, canvas.height);
        }
      } catch (error) {
        console.error("Face detection error:", error);
      }

      // Continue the detection loop
      animationFrameId = requestAnimationFrame(detect);
    };

    // Start detection
    console.log("Starting face detection loop");
    detect();

    // Cleanup
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [
    isReady,
    isActive,
    isVideoReady,
    videoRef,
    canvasRef,
    detectFaces,
    drawDetections,
  ]);

  // Update canvas size when video size changes
  useEffect(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;

    const updateCanvasSize = () => {
      if (video.videoWidth > 0 && video.videoHeight > 0) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas.style.width = `${video.offsetWidth}px`;
        canvas.style.height = `${video.offsetHeight}px`;
      }
    };

    // Initial update
    updateCanvasSize();

    // Update on resize
    const resizeObserver = new ResizeObserver(updateCanvasSize);
    resizeObserver.observe(video);

    return () => {
      resizeObserver.disconnect();
    };
  }, [videoRef]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute top-0 left-0 pointer-events-none z-10"
      style={{
        width: "100%",
        height: "100%",
      }}
    />
  );
};

export default FaceDetectionOverlay;

"use client";
import FaceDetectionOverlay from "@/components/face-detection-overlay";
import FaceCapture from "@/components/face-capture";
import WebcamCapture from "@/components/webcam-capture";
import { useFaceDetection } from "@/hooks/use-face-detection";

import React, { useState } from "react";

export default function Home() {
  const {
    isLoading: faceDetectionLoading,
    hasModels,
    error: faceDetectionError,
  } = useFaceDetection();
  const videoRef = React.useRef<HTMLVideoElement | null>(null);
  const canvasRef = React.useRef<HTMLCanvasElement | null>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  if (!faceDetectionLoading && !hasModels) {
    return (
      <div>
        <p>
          Face detection models not found. Please download the required model
          files.
        </p>
        {faceDetectionError && <p>{faceDetectionError}</p>}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        {faceDetectionLoading && (
          <div className="text-center mb-4">
            <p>Loading face detection models...</p>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-8 items-start justify-center">
          {/* Video Section */}
          <div className="flex flex-col items-center">
            <div className="aspect-video max-w-[640px] max-h-[480px] relative bg-black rounded-lg overflow-hidden">
              <WebcamCapture
                videoRef={videoRef}
                setIsCameraActive={setIsCameraActive}
              />
              <FaceDetectionOverlay
                canvasRef={canvasRef}
                videoRef={videoRef}
                isActive={isCameraActive}
              />
            </div>
          </div>

          {/* Capture Section */}
          <div className="flex-shrink-0">
            <FaceCapture videoRef={videoRef} isActive={isCameraActive} />
          </div>
        </div>
      </div>
    </div>
  );
}

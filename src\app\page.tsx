"use client";
import FaceDetectionOverlay from "@/components/face-detection-overlay";
import WebcamCapture from "@/components/webcam-capture";
import { useFaceDetection } from "@/hooks/use-face-detection";

import React, { useState } from "react";

export default function Home() {
  const {
    isLoading: faceDetectionLoading,
    hasModels,
    error: faceDetectionError,
  } = useFaceDetection();
  const videoRef = React.useRef<HTMLVideoElement | null>(null);
  const canvasRef = React.useRef<HTMLCanvasElement | null>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  if (!faceDetectionLoading && !hasModels) {
    return (
      <div>
        <p>
          Face detection models not found. Please download the required model
          files.
        </p>
        {faceDetectionError && <p>{faceDetectionError}</p>}
      </div>
    );
  }

  return (
    <div className="relative flex justify-center items-center">
      {faceDetectionLoading && <p>Loading face detection models...</p>}
      <div className="aspect-video max-w-[640px] max-h-[480px] relative">
        <WebcamCapture
          videoRef={videoRef}
          setIsCameraActive={setIsCameraActive}
        />
        <FaceDetectionOverlay
          canvasRef={canvasRef}
          videoRef={videoRef}
          isActive={isCameraActive}
        />
      </div>
    </div>
  );
}
